import path from 'path';
import { fileURLToPath } from 'url';
import Database from 'better-sqlite3';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 初始化数据库连接
const dbPath = path.join(__dirname, '..', 'chat.db');
const db = new Database(dbPath);

// 清理访客角色
function cleanupGuestRole() {
  try {
    console.log('开始清理访客角色...');

    // 检查是否存在访客角色
    const checkRole = db.prepare('SELECT * FROM roles WHERE name = ?');
    const guestRole = checkRole.get('guest');

    if (!guestRole) {
      console.log('访客角色不存在，无需清理');
      return;
    }

    console.log('找到访客角色:', guestRole.display_name);

    // 开始事务
    const transaction = db.transaction(() => {
      // 1. 删除用户-角色关联
      const deleteUserRoles = db.prepare('DELETE FROM user_roles WHERE role_id = ?');
      const userRoleResult = deleteUserRoles.run(guestRole.id);
      console.log(`删除了 ${userRoleResult.changes} 个用户-角色关联`);

      // 2. 删除角色-权限关联
      const deleteRolePermissions = db.prepare('DELETE FROM role_permissions WHERE role_id = ?');
      const rolePermissionResult = deleteRolePermissions.run(guestRole.id);
      console.log(`删除了 ${rolePermissionResult.changes} 个角色-权限关联`);

      // 3. 删除角色
      const deleteRole = db.prepare('DELETE FROM roles WHERE id = ?');
      const roleResult = deleteRole.run(guestRole.id);
      console.log(`删除了 ${roleResult.changes} 个角色`);
    });

    // 执行事务
    transaction();

    console.log('✅ 访客角色清理完成');

  } catch (error) {
    console.error('❌ 清理访客角色失败:', error);
    throw error;
  }
}

// 主程序入口
async function main() {
  try {
    console.log('=== 开始清理访客角色 ===');
    console.log('');

    cleanupGuestRole();

    console.log('');
    console.log('=== 清理完成 ===');
    console.log('');
    console.log('🎉 访客角色已成功清理!');
    console.log('');
    console.log('现在系统只保留以下角色:');
    console.log('   - 超级管理员 (superadmin)');
    console.log('   - 管理员 (admin)');
    console.log('   - 普通用户 (user)');
    console.log('');

  } catch (error) {
    console.error('❌ 清理过程失败:', error);
    process.exit(1);
  } finally {
    db.close();
    console.log('数据库连接已关闭');
  }
}

// 运行主程序
main().catch(error => {
  console.error('程序执行失败:', error);
  process.exit(1);
});
