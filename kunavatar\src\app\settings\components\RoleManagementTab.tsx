'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Plus, Edit, Trash2, Shield, Users } from 'lucide-react';
import Modal from '@/components/Modal';
import { PageLoading } from '@/components/Loading';
import { useNotification } from '@/components/notification';
import { useAuthErrorHand<PERSON> } from '@/lib/utils/auth-utils';

interface Role {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  is_system: boolean;
  user_count: number;
  permission_count: number;
  permissions: Permission[];
  created_at: string;
  updated_at: string;
}

interface Permission {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  resource: string;
  action: string;
}

interface RoleForm {
  name: string;
  display_name: string;
  description: string;
  permissions: number[];
}

export function RoleManagementTab() {
  const { success, error: notifyError } = useNotification();
  const { handleAuthError } = useAuthErrorHandler();
  
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // 模态框状态
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showPermissionModal, setShowPermissionModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  
  // 表单状态
  const [roleForm, setRoleForm] = useState<RoleForm>({
    name: '',
    display_name: '',
    description: '',
    permissions: [],
  });

  const updateRoleForm = useCallback((updates: Partial<RoleForm>) => {
    setRoleForm(prev => ({ ...prev, ...updates }));
  }, []);

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('accessToken');
      if (!token) {
        setError('请先登录');
        return;
      }

      const response = await fetch('/api/roles', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          handleAuthError();
          return;
        }
        throw new Error('获取角色列表失败');
      }

      const data = await response.json();
      if (data.success) {
        setRoles(data.roles);
      } else {
        setError(data.error || '获取角色列表失败');
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      setError(error instanceof Error ? error.message : '获取角色列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/permissions', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          handleAuthError();
          return;
        }
      } else {
        const data = await response.json();
        if (data.success) {
          setPermissions(data.permissions);
        }
      }
    } catch (error) {
      console.error('获取权限列表失败:', error);
    }
  };

  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, []);

  // 创建角色
  const handleCreateRole = async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch('/api/roles', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(roleForm),
      });

      const data = await response.json();
      if (data.success) {
        setShowCreateModal(false);
        setRoleForm({
          name: '',
          display_name: '',
          description: '',
          permissions: [],
        });
        fetchRoles();
        success('创建成功', '角色创建成功');
      } else {
        notifyError('创建失败', data.error || '创建角色失败');
      }
    } catch (error) {
      console.error('创建角色失败:', error);
      notifyError('创建失败', '创建角色失败');
    }
  };

  // 更新角色权限
  const handleUpdateRolePermissions = async () => {
    if (!selectedRole) return;

    try {
      const token = localStorage.getItem('accessToken');
      const response = await fetch(`/api/roles/${selectedRole.id}/permissions`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ permissions: roleForm.permissions.map(String) }),
      });

      const data = await response.json();
      if (data.success) {
        setShowPermissionModal(false);
        setSelectedRole(null);
        fetchRoles();
        success('权限更新成功', '角色权限更新成功');
      } else {
        notifyError('权限更新失败', data.error || '更新角色权限失败');
      }
    } catch (error) {
      console.error('更新角色权限失败:', error);
      notifyError('权限更新失败', '更新角色权限失败');
    }
  };

  // 打开创建角色模态框
  const openCreateModal = () => {
    setRoleForm({
      name: '',
      display_name: '',
      description: '',
      permissions: [],
    });
    setShowCreateModal(true);
  };

  // 打开编辑角色模态框
  const openEditModal = (role: Role) => {
    setSelectedRole(role);
    setRoleForm({
      name: role.name,
      display_name: role.display_name,
      description: role.description || '',
      permissions: role.permissions.map(p => p.id),
    });
    setShowEditModal(true);
  };

  // 打开权限管理模态框
  const openPermissionModal = (role: Role) => {
    setSelectedRole(role);
    setRoleForm({
      ...roleForm,
      permissions: role.permissions.map(p => p.id),
    });
    setShowPermissionModal(true);
  };

  if (loading) {
    return (
      <PageLoading 
        text="正在加载角色列表..." 
        fullScreen={false}
      />
    );
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-theme-foreground">角色管理</h2>
          <p className="text-sm text-theme-foreground-muted mt-1">
            管理系统角色和权限分配
          </p>
        </div>
        <button 
          onClick={openCreateModal}
          className="bg-theme-primary text-white px-4 py-2 rounded-lg hover:bg-theme-primary-hover flex items-center gap-2 transition-colors"
        >
          <Plus className="w-4 h-4" />
          创建角色
        </button>
      </div>

      {/* 错误提示 */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      {/* 角色列表 */}
      <div className="bg-theme-card rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-theme-border">
          <thead className="bg-theme-background-secondary">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-theme-foreground-muted uppercase tracking-wider">
                角色信息
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-theme-foreground-muted uppercase tracking-wider">
                用户数量
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-theme-foreground-muted uppercase tracking-wider">
                权限数量
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-theme-foreground-muted uppercase tracking-wider">
                类型
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-theme-foreground-muted uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody className="bg-theme-card divide-y divide-theme-border">
            {roles.map((role) => (
              <tr key={role.id} className="hover:bg-theme-background-secondary transition-colors">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div>
                    <div className="text-sm font-medium text-theme-foreground">
                      {role.display_name}
                    </div>
                    <div className="text-sm text-theme-foreground-muted">
                      {role.name}
                    </div>
                    {role.description && (
                      <div className="text-xs text-theme-foreground-muted mt-1">
                        {role.description}
                      </div>
                    )}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-theme-foreground">
                    <Users className="w-4 h-4 mr-1" />
                    {role.user_count}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center text-sm text-theme-foreground">
                    <Shield className="w-4 h-4 mr-1" />
                    {role.permission_count}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    role.is_system 
                      ? 'bg-blue-100 text-blue-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {role.is_system ? '系统角色' : '自定义角色'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end gap-2">
                    <button 
                      onClick={() => openPermissionModal(role)}
                      className="text-theme-primary hover:text-theme-primary-hover transition-colors"
                      title="管理权限"
                    >
                      <Shield className="w-4 h-4" />
                    </button>
                    {!role.is_system && (
                      <>
                        <button 
                          onClick={() => openEditModal(role)}
                          className="text-theme-primary hover:text-theme-primary-hover transition-colors"
                          title="编辑角色"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button 
                          className="text-red-600 hover:text-red-800 transition-colors"
                          title="删除角色"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* 创建角色模态框 */}
      <Modal
        open={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        title="创建角色"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-theme-foreground-muted mb-1">
              角色名称
            </label>
            <input
              type="text"
              value={roleForm.name}
              onChange={(e) => updateRoleForm({ name: e.target.value })}
              className="form-input-base w-full"
              placeholder="例如: editor"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-theme-foreground-muted mb-1">
              显示名称
            </label>
            <input
              type="text"
              value={roleForm.display_name}
              onChange={(e) => updateRoleForm({ display_name: e.target.value })}
              className="form-input-base w-full"
              placeholder="例如: 编辑者"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-theme-foreground-muted mb-1">
              描述
            </label>
            <textarea
              value={roleForm.description}
              onChange={(e) => updateRoleForm({ description: e.target.value })}
              className="form-input-base w-full"
              rows={3}
              placeholder="角色描述..."
            />
          </div>
          <div className="flex gap-2 pt-4">
            <button
              onClick={handleCreateRole}
              className="btn-base btn-primary flex-1"
            >
              创建角色
            </button>
            <button
              onClick={() => setShowCreateModal(false)}
              className="btn-base btn-secondary flex-1"
            >
              取消
            </button>
          </div>
        </div>
      </Modal>

      {/* 权限管理模态框 */}
      <Modal
        open={showPermissionModal}
        onClose={() => setShowPermissionModal(false)}
        title="管理角色权限"
      >
        {selectedRole && (
          <div className="space-y-4">
            <div>
              <p className="text-sm text-theme-foreground-muted mb-4">
                为角色 "{selectedRole.display_name}" 分配权限
              </p>
              <div className="space-y-3 max-h-64 overflow-y-auto border border-theme-border rounded-lg p-3">
                {permissions.map((permission) => (
                  <div key={permission.id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`permission-${permission.id}`}
                      checked={roleForm.permissions.includes(permission.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          updateRoleForm({ permissions: [...roleForm.permissions, permission.id] });
                        } else {
                          updateRoleForm({ permissions: roleForm.permissions.filter(p => p !== permission.id) });
                        }
                      }}
                      className="rounded border-theme-border"
                    />
                    <label htmlFor={`permission-${permission.id}`} className="ml-2 text-sm">
                      <span className="font-medium text-theme-foreground">{permission.display_name}</span>
                      <span className="text-theme-foreground-muted ml-2">({permission.name})</span>
                      {permission.description && (
                        <div className="text-xs text-theme-foreground-muted mt-1">
                          {permission.description}
                        </div>
                      )}
                    </label>
                  </div>
                ))}
              </div>
            </div>
            <div className="flex gap-2 pt-4">
              <button
                onClick={handleUpdateRolePermissions}
                className="btn-base btn-primary flex-1"
              >
                更新权限
              </button>
              <button
                onClick={() => setShowPermissionModal(false)}
                className="btn-base btn-secondary flex-1"
              >
                取消
              </button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}
