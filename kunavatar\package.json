{"name": "kun-agent", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "init-admin": "node scripts/init-admin.js", "cleanup-guest": "node scripts/cleanup-guest-role.js", "mcp-server": "node start-mcp-server.js", "mcp-dev": "npx tsx src/lib/mcp/mcp-server.ts", "mcp-ts": "ts-node --project tsconfig.mcp.json src/lib/mcp/mcp-server.ts"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.15.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^22", "@types/node-fetch": "^2.6.12", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-syntax-highlighter": "^15.5.13", "@types/sqlite3": "^3.1.11", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.0", "bcryptjs": "^3.0.2", "better-sqlite3": "^11.5.0", "framer-motion": "^12.18.1", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.460.0", "next": "15.3.3", "node-fetch": "^3.3.2", "postcss": "^8.4.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-highlight": "^7.0.2", "rehype-raw": "^7.0.0", "remark-gfm": "^4.0.1", "sqlite3": "^5.1.7", "tailwindcss": "^3.4.0", "typescript": "^5", "uuid": "^11.1.0", "zod": "^3.22.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@types/better-sqlite3": "^7.6.8", "eslint": "^9", "eslint-config-next": "15.3.3", "ts-node": "^10.9.0", "tsx": "^4.20.0"}}