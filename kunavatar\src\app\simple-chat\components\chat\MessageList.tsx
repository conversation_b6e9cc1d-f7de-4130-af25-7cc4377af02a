'use client';

import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Bot, User, Info, ChevronDown, ChevronUp, Clock } from 'lucide-react';
import { ThinkingMode, hasThinkingContent, removeThinkingContent } from '../ui/ThinkingMode';
import StreamedContent from '../ui/StreamedContent';
import { ToolCallMessage } from '../tools/ToolCallMessage';
import { ChatStyle } from '../input-controls';
import { SimpleMessage } from '../../types';
import ModelLogo from '@/app/model-manager/components/ModelLogo';
import { AgentWithRelations } from '@/app/agents/types';

interface MessageListProps {
  messages: SimpleMessage[];
  isStreaming: boolean;
  expandedThinkingMessages: Set<string>;
  onToggleThinkingExpand: (messageId: string) => void;
  chatStyle: ChatStyle;
  selectedModel?: string;
  // 重新添加customModels以支持正确的模型显示
  customModels?: Array<{
    base_model: string;
    display_name: string;
    family?: string;
  }>;
  // 智能体信息
  selectedAgent?: AgentWithRelations | null;
}

export function MessageList({
  messages,
  isStreaming,
  expandedThinkingMessages,
  onToggleThinkingExpand,
  chatStyle,
  selectedModel,
  customModels,
  selectedAgent,
}: MessageListProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [isNearBottom, setIsNearBottom] = useState(true);
  const [isNearTop, setIsNearTop] = useState(true);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [messageCount, setMessageCount] = useState(messages.length);
  const [userScrolled, setUserScrolled] = useState(false);
  const [lastScrollTime, setLastScrollTime] = useState(0);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 获取模型的显示信息 - 修复：正确查找customModels
  const getModelDisplayInfo = (modelName?: string) => {
    if (!modelName) return { displayName: 'AI助手', family: 'default' };
    
    // 查找对应的自定义模型信息
    const customModel = customModels?.find(m => m.base_model === modelName);
    
    return {
      displayName: customModel?.display_name || modelName,
      family: customModel?.family || modelName.split(':')[0] || 'default'
    };
  };

  // 检查滚动位置 - 寻找真正的滚动容器
  const checkScrollPosition = useCallback(() => {
    // 先尝试当前组件的滚动容器
    let container: HTMLElement | null = scrollContainerRef.current;
    
    // 如果当前容器没有滚动条，查找父级的滚动容器
    if (container && container.scrollHeight <= container.clientHeight) {
      // 查找最近的可滚动父元素
      let parent = container.parentElement;
      while (parent) {
        if (parent.scrollHeight > parent.clientHeight && 
            getComputedStyle(parent).overflowY !== 'visible') {
          container = parent;
          break;
        }
        parent = parent.parentElement;
      }
    }
    
    if (!container) return { nearBottom: true, nearTop: true };

    const { scrollTop, scrollHeight, clientHeight } = container;
    const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
    const distanceFromTop = scrollTop;
    
    // 检测是否接近顶部和底部 - 考虑段落间距影响
    // space-y-4 = 16px，加上padding和其他间距，使用更宽松的阈值
    const nearBottom = distanceFromBottom <= 50; // 放宽底部检测，应对段落间距
    const nearTop = distanceFromTop <= 50;
    
    // 智能显示按钮：当有足够内容可以滚动时就显示
    const hasEnoughContentToScroll = scrollHeight > clientHeight + 100; // 内容高度超过容器高度100px以上
    const showButtons = messages.length > 0 && hasEnoughContentToScroll;
    
    setIsNearBottom(nearBottom);
    setIsNearTop(nearTop);
    setShowScrollButtons(showButtons);
    
    return { nearBottom, nearTop };
  }, [messages.length]);

  // 滚动到底部 - 优化定位精度
  const scrollToBottom = useCallback((behavior: 'auto' | 'smooth' = 'smooth') => {
    // 方法1：使用 scrollIntoView，但加上 block: 'end' 确保精确定位
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ 
        behavior, 
        block: 'end',
        inline: 'nearest' 
      });
      return;
    }
    
    // 方法2：备用方案，直接滚动到容器底部
    const container = scrollContainerRef.current?.parentElement;
    if (container && container.scrollHeight > container.clientHeight) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior
      });
    }
  }, []);

  // 滚动到顶部
  const scrollToTop = useCallback((behavior: 'auto' | 'smooth' = 'smooth') => {
    // 寻找可滚动的容器
    let container: HTMLElement | null = scrollContainerRef.current;
    
    // 如果当前容器不可滚动，查找父级滚动容器
    if (container && container.scrollHeight <= container.clientHeight) {
      let parent = container.parentElement;
      while (parent) {
        if (parent.scrollHeight > parent.clientHeight && 
            getComputedStyle(parent).overflowY !== 'visible') {
          container = parent;
          break;
        }
        parent = parent.parentElement;
      }
    }
    
    if (container) {
      container.scrollTo({ top: 0, behavior });
    }
  }, []);

  // 处理用户滚动 - 增加用户意图检测
  const handleScroll = useCallback(() => {
    const now = Date.now();
    const timeSinceLastScroll = now - lastScrollTime;
    
    // 如果距离上次滚动时间很短，认为是用户主动滚动
    if (timeSinceLastScroll < 1000) { // 1秒内的滚动认为是用户行为
      setUserScrolled(true);
      
      // 清除之前的定时器
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      
      // 3秒后重置用户滚动状态
      scrollTimeoutRef.current = setTimeout(() => {
        setUserScrolled(false);
      }, 3000);
    }
    
    setLastScrollTime(now);
    // 直接内联检查滚动位置，避免循环依赖
    requestAnimationFrame(() => {
      // 内联检查位置逻辑，避免函数依赖
      let container: HTMLElement | null = scrollContainerRef.current;
      
      if (container && container.scrollHeight <= container.clientHeight) {
        let parent = container.parentElement;
        while (parent) {
          if (parent.scrollHeight > parent.clientHeight && 
              getComputedStyle(parent).overflowY !== 'visible') {
            container = parent;
            break;
          }
          parent = parent.parentElement;
        }
      }
      
      if (container) {
        const { scrollTop, scrollHeight, clientHeight } = container;
        const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
        const distanceFromTop = scrollTop;
        
        const nearBottom = distanceFromBottom <= 50;
        const nearTop = distanceFromTop <= 50;
        const hasEnoughContentToScroll = scrollHeight > clientHeight + 100;
        const showButtons = messages.length > 0 && hasEnoughContentToScroll;
        
        setIsNearBottom(nearBottom);
        setIsNearTop(nearTop);
        setShowScrollButtons(showButtons);
      }
    });
  }, [lastScrollTime, messages.length]); // 移除checkScrollPosition依赖，直接依赖messages.length

  // 监听外层滚动容器的滚动事件
  useEffect(() => {
    // 找到外层的滚动容器并绑定事件
    const findScrollContainer = () => {
      let current = scrollContainerRef.current;
      if (!current) return null;
      
      // 向上找到真正的滚动容器
      let parent = current.parentElement;
      while (parent) {
        if (parent.scrollHeight > parent.clientHeight && 
            getComputedStyle(parent).overflowY !== 'visible') {
          return parent;
        }
        parent = parent.parentElement;
      }
      return current;
    };

    // 直接定义滚动处理函数，避免依赖handleScroll导致频繁重新绑定
    const scrollHandler = () => {
      const now = Date.now();
      const timeSinceLastScroll = now - lastScrollTime;
      
      // 如果距离上次滚动时间很短，认为是用户主动滚动
      if (timeSinceLastScroll < 1000) {
        setUserScrolled(true);
        
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
        
        scrollTimeoutRef.current = setTimeout(() => {
          setUserScrolled(false);
        }, 3000);
      }
      
      setLastScrollTime(now);
      
      // 检查滚动位置
      requestAnimationFrame(() => {
        let container: HTMLElement | null = scrollContainerRef.current;
        
        if (container && container.scrollHeight <= container.clientHeight) {
          let parent = container.parentElement;
          while (parent) {
            if (parent.scrollHeight > parent.clientHeight && 
                getComputedStyle(parent).overflowY !== 'visible') {
              container = parent;
              break;
            }
            parent = parent.parentElement;
          }
        }
        
        if (container) {
          const { scrollTop, scrollHeight, clientHeight } = container;
          const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
          const distanceFromTop = scrollTop;
          
          const nearBottom = distanceFromBottom <= 50;
          const nearTop = distanceFromTop <= 50;
          const hasEnoughContentToScroll = scrollHeight > clientHeight + 100;
          const showButtons = messages.length > 0 && hasEnoughContentToScroll;
          
          setIsNearBottom(nearBottom);
          setIsNearTop(nearTop);
          setShowScrollButtons(showButtons);
        }
      });
    };

    const scrollContainer = findScrollContainer();
    if (scrollContainer) {
      scrollContainer.addEventListener('scroll', scrollHandler, { passive: true });
      return () => {
        scrollContainer.removeEventListener('scroll', scrollHandler);
      };
    }
  }, []); // 空依赖数组，避免频繁重新绑定

  // 当消息发生变化时的智能滚动逻辑
  useEffect(() => {
    const wasNearBottom = isNearBottom;
    const newMessageCount = messages.length;
    const hasNewMessages = newMessageCount > messageCount;
    
    // 更新消息计数
    setMessageCount(newMessageCount);
    
    // 优化的智能滚动逻辑：
    // 1. 用户主动滚动时，暂停自动滚动
    // 2. 只有在接近底部且没有用户干预时才自动滚动
    // 3. 流式更新使用防抖机制，减少频繁滚动
    
    if (!userScrolled && wasNearBottom && (hasNewMessages || isStreaming)) {
      // 清除之前的滚动定时器，实现防抖
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
      
      // 使用防抖延迟，避免频繁滚动导致的抖动
      const scrollDelay = isStreaming ? 150 : 50; // 流式时更长延迟
      
      scrollTimeoutRef.current = setTimeout(() => {
        // 再次检查用户是否在此期间滚动了
        if (!userScrolled && isNearBottom) {
          // 流式更新时使用 'auto'，新消息时使用 'smooth'
          const behavior = isStreaming ? 'auto' : 'smooth';
          scrollToBottom(behavior);
        }
      }, scrollDelay);
    }
    
    // 延迟重新检查位置，避免与滚动冲突
    setTimeout(() => {
      requestAnimationFrame(() => {
        // 内联检查位置逻辑，避免函数依赖导致循环
        let container: HTMLElement | null = scrollContainerRef.current;
        
        if (container && container.scrollHeight <= container.clientHeight) {
          let parent = container.parentElement;
          while (parent) {
            if (parent.scrollHeight > parent.clientHeight && 
                getComputedStyle(parent).overflowY !== 'visible') {
              container = parent;
              break;
            }
            parent = parent.parentElement;
          }
        }
        
        if (container) {
          const { scrollTop, scrollHeight, clientHeight } = container;
          const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
          const distanceFromTop = scrollTop;
          
          const nearBottom = distanceFromBottom <= 50;
          const nearTop = distanceFromTop <= 50;
          const hasEnoughContentToScroll = scrollHeight > clientHeight + 100;
          const showButtons = messages.length > 0 && hasEnoughContentToScroll;
          
          setIsNearBottom(nearBottom);
          setIsNearTop(nearTop);
          setShowScrollButtons(showButtons);
        }
      });
    }, 200);
  }, [messages.length, isStreaming, isNearBottom, messageCount, userScrolled, scrollToBottom]); // 移除checkScrollPosition依赖

  // 初始化时滚动到底部
  useEffect(() => {
    if (messages.length > 0) {
      // 首次加载时直接滚动到底部
      requestAnimationFrame(() => {
        scrollToBottom('auto');
      });
    }
  }, [messages.length, scrollToBottom]); // 依赖messages.length和scrollToBottom

  // 组件挂载后立即检查滚动位置
  useEffect(() => {
    // 延迟检查确保DOM完全渲染
    const timer = setTimeout(() => {
      // 内联检查逻辑，避免函数依赖
      let container: HTMLElement | null = scrollContainerRef.current;
      
      if (container && container.scrollHeight <= container.clientHeight) {
        let parent = container.parentElement;
        while (parent) {
          if (parent.scrollHeight > parent.clientHeight && 
              getComputedStyle(parent).overflowY !== 'visible') {
            container = parent;
            break;
          }
          parent = parent.parentElement;
        }
      }
      
      if (!container) return;

      const { scrollTop, scrollHeight, clientHeight } = container;
      const distanceFromBottom = scrollHeight - (scrollTop + clientHeight);
      const distanceFromTop = scrollTop;
      
      const nearBottom = distanceFromBottom <= 50;
      const nearTop = distanceFromTop <= 50;
      const hasEnoughContentToScroll = scrollHeight > clientHeight + 100;
      const showButtons = messages.length > 0 && hasEnoughContentToScroll;
      
      setIsNearBottom(nearBottom);
      setIsNearTop(nearTop);
      setShowScrollButtons(showButtons);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [messages.length]); // 只依赖消息长度

  // 清理定时器
  useEffect(() => {
    return () => {
      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }
    };
  }, []);

  // 格式化时间（纳秒转秒）
  const formatDuration = (nanoseconds?: number) => {
    if (!nanoseconds) return null;
    const seconds = (nanoseconds / 1000000000).toFixed(2);
    return `${seconds}s`;
  };

  // 渲染生成统计信息图标
  const renderGenerationStatsIcon = (message: SimpleMessage) => {
    // 检查是否为当前生成中的消息
    const isCurrentlyGenerating = isStreaming && 
      message.role === 'assistant' && 
      messages.indexOf(message) === messages.length - 1;
    
    // 如果有完整的统计数据（至少有总时长或生成token数量），显示详细信息
    const hasCompleteStats = message.total_duration || message.eval_count;
    
    const statsText = hasCompleteStats
       ? `总时长: ${formatDuration(message.total_duration)}\n` +
         `加载时长: ${formatDuration(message.load_duration)}\n` +
         `提示词处理: ${message.prompt_eval_count || 0} tokens\n` +
         `生成内容: ${message.eval_count || 0} tokens\n` +
         `提示词速度: ${message.prompt_eval_duration && message.prompt_eval_count ? (message.prompt_eval_count / (message.prompt_eval_duration / 1000000000)).toFixed(1) : 0} tokens/s\n` +
         `生成速度: ${message.eval_duration && message.eval_count ? (message.eval_count / (message.eval_duration / 1000000000)).toFixed(1) : 0} tokens/s`
       : isCurrentlyGenerating 
         ? '正在生成中，统计信息将在完成后显示...'
         : '统计信息不可用';

    // 获取消息创建时间
    const messageTime = message.timestamp 
      ? new Date(message.timestamp).toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        })
      : new Date().toLocaleTimeString('zh-CN', {
          hour: '2-digit',
          minute: '2-digit'
        });

    return (
      <div className="flex items-center gap-2">
        <div className="relative inline-block">
          <div className="group inline-block">
            <Info className="w-4 h-4 text-theme-foreground-muted hover:text-theme-foreground cursor-help transition-colors" />
            <div className="absolute left-0 bottom-full mb-1 bg-gray-800 text-white text-xs rounded px-3 py-2 whitespace-pre-line opacity-0 group-hover:opacity-100 transition-opacity duration-200 z-50 min-w-max shadow-lg pointer-events-none">
              {statsText}
            </div>
          </div>
        </div>
        {/* 消息时间显示在图标右侧 */}
        <span className="text-xs text-theme-foreground-muted">
          {messageTime}
        </span>
      </div>
    );
  };

  return (
    <div className="relative">
      <div 
        ref={scrollContainerRef}
        className="p-4 space-y-4"
      >
        {messages.map((message, index) => {
          // 如果是工具调用占位符消息，渲染工具调用组件
          if (message.role === 'tool_call' && message.toolCall) {
            return (
              <ToolCallMessage key={message.id} toolCall={message.toolCall} />
            );
          }
          
          // 检查消息是否包含思考内容
          const hasThinking = message.role === 'assistant' && hasThinkingContent(message.content);
          const contentWithoutThinking = hasThinking ? removeThinkingContent(message.content) : message.content;
          const isCurrentlyThinking = isStreaming && message.role === 'assistant' && index === messages.length - 1 && hasThinkingContent(message.content) && !removeThinkingContent(message.content).trim();
          
          // 🔧 修复：检查是否应该显示消息气泡
          // 对于 assistant 消息，如果只有思考内容而没有实际内容，且不是正在生成状态，则不显示消息气泡
          const isLastMessage = index === messages.length - 1;
          const isGenerating = isStreaming && message.role === 'assistant' && isLastMessage;
          const hasActualContent = contentWithoutThinking.trim().length > 0;
          const shouldShowBubble = message.role === 'user' || hasActualContent || (isGenerating && !isCurrentlyThinking);
          
          // 获取模型显示信息
          const modelDisplayInfo = getModelDisplayInfo(message.model || selectedModel);
          
          // 根据聊天样式决定布局
          if (chatStyle === 'conversation') {
            // 对话模式：用户右侧，AI左侧
            const isUser = message.role === 'user';
            return (
              <div key={message.id} className={`flex gap-3 ${isUser ? 'flex-row-reverse' : ''}`}>
                <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${
                  isUser 
                    ? 'bg-theme-primary text-white' 
                    : 'bg-theme-card border border-theme-border text-theme-foreground'
                }`}>
                  {isUser ? (
                    <User className="w-5 h-5" />
                  ) : (
                    selectedAgent && selectedAgent.avatar ? (
                      <img 
                        src={selectedAgent.avatar} 
                        alt={selectedAgent.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <ModelLogo 
                        modelName={modelDisplayInfo.family}
                        size="lg"
                        containerSize={40}
                        imageSize={32}
                        className="bg-transparent border-0 rounded-full"
                      />
                    )
                  )}
                </div>
                <div className={`max-w-[80%] space-y-2 ${isUser ? 'flex flex-col items-end' : ''}`}>
                  {/* 只有在需要显示消息气泡或者有思考内容时才显示角色标识 */}
                  {(shouldShowBubble || hasThinking || isCurrentlyThinking) && (
                    <div className={`flex items-center gap-2 ${isUser ? 'justify-end' : 'justify-start'}`}>
                      <div className={`text-sm text-theme-foreground-muted ${isUser ? 'text-right' : 'text-left'}`}>
                        {isUser ? '你' : (selectedAgent ? selectedAgent.name : modelDisplayInfo.displayName)}
                      </div>
                      {/* AI消息的统计信息 */}
                      {!isUser && message.role === 'assistant' && renderGenerationStatsIcon(message)}
                    </div>
                  )}
                  
                  {/* 思考面板 - 只对AI消息显示 */}
                  {message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && (
                    <ThinkingMode
                      content={message.content}
                      isExpanded={expandedThinkingMessages.has(message.id)}
                      onToggleExpand={() => onToggleThinkingExpand(message.id)}
                      defaultHidden={true}
                    />
                  )}
                  
                  {/* 消息气泡 - 只有在应该显示时才渲染 */}
                  {shouldShowBubble && (
                    <div className={`inline-block p-3 rounded-lg ${
                      isUser 
                        ? 'bg-theme-primary text-white' 
                        : 'text-theme-foreground'
                    }`}>
                      {isGenerating && !isCurrentlyThinking && !contentWithoutThinking ? (
                        <div className="flex items-center gap-2">
                          <div className="spinner-small">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                          </div>
                          <span className="text-sm text-theme-foreground-muted">loading...</span>
                        </div>
                      ) : (
                        <StreamedContent
                          content={contentWithoutThinking || ''}
                          isStreaming={isGenerating}
                          enableMarkdown={!isUser} // 重新启用：只对AI助手的消息启用markdown渲染
                          className={!isUser ? "break-words leading-[1.4]" : "break-words whitespace-pre-wrap leading-[1.4]"}
                          style={{
                            minWidth: 0,
                            maxWidth: '100%',
                          }}
                        />
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          } else {
            // 助手模式：所有消息都在左侧
            return (
              <div key={message.id} className="flex gap-3">
                <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${
                  message.role === 'user' 
                    ? 'bg-theme-primary text-white' 
                    : 'bg-theme-card border border-theme-border text-theme-foreground'
                }`}>
                  {message.role === 'user' ? (
                    <User className="w-5 h-5" />
                  ) : (
                    selectedAgent && selectedAgent.avatar ? (
                      <img 
                        src={selectedAgent.avatar} 
                        alt={selectedAgent.name}
                        className="w-10 h-10 rounded-full object-cover"
                      />
                    ) : (
                      <ModelLogo 
                        modelName={modelDisplayInfo.family}
                        size="lg"
                        containerSize={40}
                        imageSize={32}
                        className="bg-transparent border-0 rounded-full"
                      />
                    )
                  )}
                </div>
                <div className="flex-1 space-y-2">
                  {/* 只有在需要显示消息气泡或者有思考内容时才显示角色标识 */}
                  {(shouldShowBubble || hasThinking || isCurrentlyThinking) && (
                    <div className="flex items-center gap-2">
                      <div className="text-sm text-theme-foreground-muted">
                        {message.role === 'user' ? '你' : (selectedAgent ? selectedAgent.name : modelDisplayInfo.displayName)}
                      </div>
                      {/* AI消息的统计信息 */}
                      {message.role === 'assistant' && renderGenerationStatsIcon(message)}
                    </div>
                  )}
                  
                  {/* 思考面板 - 只对AI消息显示 */}
                  {message.role === 'assistant' && (hasThinking || isCurrentlyThinking) && (
                    <ThinkingMode
                      content={message.content}
                      isExpanded={expandedThinkingMessages.has(message.id)}
                      onToggleExpand={() => onToggleThinkingExpand(message.id)}
                      defaultHidden={true}
                    />
                  )}
                  
                  {/* 正常内容显示 - 只有在应该显示时才渲染 */}
                  {shouldShowBubble && (
                    <div className="prose prose-sm max-w-none text-theme-foreground">
                      {isGenerating && !isCurrentlyThinking && !contentWithoutThinking ? (
                        <div className="flex items-center gap-2">
                          <div className="spinner-small">
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                            <div></div>
                          </div>
                          <span className="text-sm text-theme-foreground-muted">loading...</span>
                        </div>
                      ) : (
                        <StreamedContent
                          content={contentWithoutThinking || ''}
                          isStreaming={isGenerating}
                          enableMarkdown={message.role === 'assistant'} // 重新启用：只对AI助手的消息启用markdown渲染
                          className={message.role === 'assistant' ? "break-words leading-[1.4]" : "break-words whitespace-pre-wrap leading-[1.4]"}
                          style={{
                            minWidth: 0,
                            maxWidth: '100%'
                          }}
                        />
                      )}
                    </div>
                  )}
                </div>
              </div>
            );
          }
        })}
        
                <div ref={messagesEndRef} />
      </div>

      {/* 浮动按钮组 - 垂直布局，固定在可视区域 */}
       {showScrollButtons && (
         <div className="group fixed bottom-40 right-12 z-50 flex flex-col gap-1 p-2">
          {/* 悬浮触发区域 - 透明但可交互 */}
          <div className="absolute inset-0 w-16 h-full -right-2"></div>
          
          {/* 按钮容器 - 默认隐藏，hover时显示 */}
          <div className="flex flex-col gap-1 opacity-0 translate-x-4 group-hover:opacity-100 group-hover:translate-x-0 transition-all duration-300 ease-out">
            {/* 回到顶部按钮 */}
            {!isNearTop && (
              <button
                onClick={() => scrollToTop('smooth')}
                className="w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm"
                title="回到顶部"
              >
                <ChevronUp className="w-4 h-4" />
              </button>
            )}
          
            {/* 调试信息按钮 */}
            <button
              onClick={() => {
                const { nearBottom, nearTop } = checkScrollPosition();
                console.log('当前状态:', { isNearTop, isNearBottom, nearTop, nearBottom, showScrollButtons });
              }}
              className="w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm"
              title="调试信息"
            >
              <Clock className="w-4 h-4" />
            </button>
          
            {/* 回到底部按钮 */}
            {!isNearBottom && (
              <button
                onClick={() => scrollToBottom('smooth')}
                className="w-10 h-10 bg-theme-background/80 hover:bg-theme-background hover:border-theme-border text-theme-foreground-muted hover:text-theme-foreground rounded-full transition-all duration-200 flex items-center justify-center border border-theme-border/50 hover:shadow-xl hover:scale-110 backdrop-blur-sm"
                title="回到底部"
              >
                <ChevronDown className="w-4 h-4" />
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
}