'use client';

import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'next/navigation';
import { CustomModel } from '@/lib/database/custom-models';

interface UseUrlHandlerProps {
  models: CustomModel[];
  selectedModel: string;
  currentConversation: any;
  conversationLoading: boolean;
  createConversation: (title: string, model: string) => Promise<number | null>;
  switchConversation: (id: number) => Promise<void>;
  setSelectedModel: (model: string) => void;
}

export function useUrlHandler({
  models,
  selectedModel,
  currentConversation,
  conversationLoading,
  createConversation,
  switchConversation,
  setSelectedModel,
}: UseUrlHandlerProps) {
  const searchParams = useSearchParams();
  const [isProcessingUrl, setIsProcessingUrl] = useState(false);
  
  // 使用ref存储函数引用，避免useEffect依赖问题
  const createConversationRef = useRef(createConversation);
  const switchConversationRef = useRef(switchConversation);
  const setSelectedModelRef = useRef(setSelectedModel);
  
  // 更新ref的值
  useEffect(() => {
    createConversationRef.current = createConversation;
    switchConversationRef.current = switchConversation;
    setSelectedModelRef.current = setSelectedModel;
  }, [createConversation, switchConversation, setSelectedModel]);

  // URL处理逻辑
  useEffect(() => {
    const handleUrlChange = async () => {
      if (isProcessingUrl) return;
      
      const shouldCreateNew = searchParams.get('new') === 'true';
      const conversationId = searchParams.get('id');
      const modelParam = searchParams.get('model');
      
      // 处理模型参数
      if (modelParam && models.length > 0) {
        const decodedModel = decodeURIComponent(modelParam);
        // 检查模型是否存在于可用模型列表中
        const modelExists = models.some(model => model.base_model === decodedModel);
        if (modelExists && selectedModel !== decodedModel) {
          console.log('🎯 从URL设置模型:', decodedModel);
          setSelectedModelRef.current(decodedModel);
        }
      }
      
      if (shouldCreateNew && models.length > 0 && selectedModel && !currentConversation && !conversationLoading) {
        setIsProcessingUrl(true);
        const title = `新对话 - ${new Date().toLocaleString('zh-CN', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })}`;
        
        try {
          // 使用当前选择的模型（可能来自URL参数）
          const modelToUse = modelParam && models.some(m => m.base_model === decodeURIComponent(modelParam))
            ? decodeURIComponent(modelParam)
            : selectedModel;
          
          const newConversationId = await createConversationRef.current(title, modelToUse);
          if (newConversationId) {
            // 更新URL，保留model参数
            const newUrl = `/simple-chat?id=${newConversationId}${modelParam ? `&model=${modelParam}` : ''}`;
            if (typeof window !== 'undefined') {
              window.history.replaceState(null, '', newUrl);
            }
          }
        } catch (err) {
          console.error('自动创建对话失败:', err);
          throw new Error('自动创建对话失败');
        } finally {
          setIsProcessingUrl(false);
        }
      } else if (conversationId && !conversationLoading && !isProcessingUrl) {
        const id = parseInt(conversationId);
        // 修复：添加更严格的条件检查，避免无限循环
        if (id && (!currentConversation || currentConversation.id !== id)) {
          setIsProcessingUrl(true);
          try {
            await switchConversationRef.current(id);
          } catch (err) {
            console.error('加载指定对话失败:', err);
            throw new Error('加载对话失败');
          } finally {
            setIsProcessingUrl(false);
          }
        }
      }
    };

    handleUrlChange().catch(error => {
      console.error('URL处理失败:', error);
    });
  }, [searchParams, models, selectedModel, conversationLoading, isProcessingUrl]); // 移除currentConversation依赖，避免无限循环

  return {
    isProcessingUrl,
    setIsProcessingUrl,
  };
}